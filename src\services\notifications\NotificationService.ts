// Notification Service for Daily Reminders

import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';
import { NOTIFICATION_CONFIG } from '@/constants';

// Configure notification behavior
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: false,
  }),
});

export interface NotificationPermissions {
  granted: boolean;
  canAskAgain: boolean;
  status: Notifications.PermissionStatus;
}

export interface ScheduledNotification {
  id: string;
  title: string;
  body: string;
  time: string;
  enabled: boolean;
}

class NotificationService {
  private static instance: NotificationService;

  private constructor() {}

  public static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  /**
   * Request notification permissions
   */
  async requestPermissions(): Promise<NotificationPermissions> {
    try {
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      // For Android, we need to create a notification channel
      if (Platform.OS === 'android') {
        await Notifications.setNotificationChannelAsync(NOTIFICATION_CONFIG.CHANNEL_ID, {
          name: NOTIFICATION_CONFIG.CHANNEL_NAME,
          importance: Notifications.AndroidImportance.HIGH,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#007AFF',
        });
      }

      return {
        granted: finalStatus === 'granted',
        canAskAgain: finalStatus !== 'denied',
        status: finalStatus,
      };
    } catch (error) {
      console.error('Error requesting notification permissions:', error);
      return {
        granted: false,
        canAskAgain: false,
        status: 'denied',
      };
    }
  }

  /**
   * Schedule daily usage reminder
   */
  async scheduleDailyReminder(time: string, enabled: boolean = true): Promise<string | null> {
    try {
      if (!enabled) {
        await this.cancelDailyReminder();
        return null;
      }

      const permissions = await this.requestPermissions();
      if (!permissions.granted) {
        throw new Error('Notification permissions not granted');
      }

      // Cancel existing reminder
      await this.cancelDailyReminder();

      // Parse time (format: "HH:MM")
      const [hours, minutes] = time.split(':').map(Number);
      
      if (isNaN(hours) || isNaN(minutes) || hours < 0 || hours > 23 || minutes < 0 || minutes > 59) {
        throw new Error('Invalid time format');
      }

      // Schedule the notification
      const notificationId = await Notifications.scheduleNotificationAsync({
        identifier: NOTIFICATION_CONFIG.DAILY_REMINDER_ID,
        content: {
          title: '⚡ Electricity Usage Reminder',
          body: "Don't forget to record your electricity usage today!",
          sound: NOTIFICATION_CONFIG.DEFAULT_SOUND,
          data: {
            type: 'daily_reminder',
            action: 'record_usage',
          },
        },
        trigger: {
          hour: hours,
          minute: minutes,
          repeats: true,
        },
      });

      console.log('Daily reminder scheduled:', notificationId);
      return notificationId;
    } catch (error) {
      console.error('Error scheduling daily reminder:', error);
      return null;
    }
  }

  /**
   * Cancel daily usage reminder
   */
  async cancelDailyReminder(): Promise<void> {
    try {
      await Notifications.cancelScheduledNotificationAsync(NOTIFICATION_CONFIG.DAILY_REMINDER_ID);
      console.log('Daily reminder cancelled');
    } catch (error) {
      console.error('Error cancelling daily reminder:', error);
    }
  }

  /**
   * Schedule low units warning
   */
  async scheduleLowUnitsWarning(currentUnits: number, threshold: number, unitType: string): Promise<string | null> {
    try {
      if (currentUnits > threshold) {
        return null; // No warning needed
      }

      const permissions = await this.requestPermissions();
      if (!permissions.granted) {
        return null;
      }

      const notificationId = await Notifications.scheduleNotificationAsync({
        identifier: NOTIFICATION_CONFIG.LOW_UNITS_WARNING_ID,
        content: {
          title: '⚠️ Low Units Warning',
          body: `Your electricity units (${currentUnits} ${unitType}) are below the threshold of ${threshold} ${unitType}. Consider purchasing more electricity.`,
          sound: NOTIFICATION_CONFIG.DEFAULT_SOUND,
          data: {
            type: 'low_units_warning',
            action: 'add_purchase',
            currentUnits,
            threshold,
          },
        },
        trigger: null, // Immediate notification
      });

      console.log('Low units warning scheduled:', notificationId);
      return notificationId;
    } catch (error) {
      console.error('Error scheduling low units warning:', error);
      return null;
    }
  }

  /**
   * Schedule monthly reset notification
   */
  async scheduleMonthlyReset(): Promise<string | null> {
    try {
      const permissions = await this.requestPermissions();
      if (!permissions.granted) {
        return null;
      }

      const notificationId = await Notifications.scheduleNotificationAsync({
        identifier: NOTIFICATION_CONFIG.MONTHLY_RESET_ID,
        content: {
          title: '📅 Monthly Reset Available',
          body: 'Your monthly electricity data can now be reset. Would you like to start fresh for the new month?',
          sound: NOTIFICATION_CONFIG.DEFAULT_SOUND,
          data: {
            type: 'monthly_reset',
            action: 'reset_data',
          },
        },
        trigger: {
          day: 1, // First day of the month
          hour: 9,
          minute: 0,
          repeats: true,
        },
      });

      console.log('Monthly reset notification scheduled:', notificationId);
      return notificationId;
    } catch (error) {
      console.error('Error scheduling monthly reset notification:', error);
      return null;
    }
  }

  /**
   * Get all scheduled notifications
   */
  async getScheduledNotifications(): Promise<Notifications.NotificationRequest[]> {
    try {
      return await Notifications.getAllScheduledNotificationsAsync();
    } catch (error) {
      console.error('Error getting scheduled notifications:', error);
      return [];
    }
  }

  /**
   * Cancel all notifications
   */
  async cancelAllNotifications(): Promise<void> {
    try {
      await Notifications.cancelAllScheduledNotificationsAsync();
      console.log('All notifications cancelled');
    } catch (error) {
      console.error('Error cancelling all notifications:', error);
    }
  }

  /**
   * Handle notification response (when user taps notification)
   */
  handleNotificationResponse(response: Notifications.NotificationResponse): void {
    const { data } = response.notification.request.content;
    
    console.log('Notification response:', data);
    
    // Handle different notification types
    switch (data?.type) {
      case 'daily_reminder':
        // Navigate to usage screen
        console.log('Navigate to usage screen');
        break;
      case 'low_units_warning':
        // Navigate to purchases screen
        console.log('Navigate to purchases screen');
        break;
      case 'monthly_reset':
        // Show reset confirmation
        console.log('Show reset confirmation');
        break;
      default:
        console.log('Unknown notification type');
    }
  }

  /**
   * Test notification (for development)
   */
  async sendTestNotification(): Promise<void> {
    try {
      const permissions = await this.requestPermissions();
      if (!permissions.granted) {
        console.log('Notification permissions not granted');
        return;
      }

      await Notifications.scheduleNotificationAsync({
        content: {
          title: '🧪 Test Notification',
          body: 'This is a test notification from Prepaid Electricity App',
          sound: NOTIFICATION_CONFIG.DEFAULT_SOUND,
        },
        trigger: { seconds: 1 },
      });

      console.log('Test notification sent');
    } catch (error) {
      console.error('Error sending test notification:', error);
    }
  }
}

export default NotificationService.getInstance();
