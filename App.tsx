import React from 'react';
import { StatusBar } from 'expo-status-bar';
import { NavigationContainer } from '@react-navigation/native';
import { Provider } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { TamaguiProvider } from '@tamagui/core';

// Tamagui config
import config from './tamagui.config';

// Store
import { store, persistor } from './src/store';

// Navigation
import AppNavigator from './src/navigation/AppNavigator';

// Components
import LoadingScreen from './src/components/common/LoadingScreen';

// Hooks
import { useAppInitialization } from './src/hooks/useAppInitialization';

// Main App Component
const AppContent: React.FC = () => {
  useAppInitialization();

  return (
    <NavigationContainer>
      <AppNavigator />
    </NavigationContainer>
  );
};

export default function App() {
  console.log('🚀 Prepaid Electricity App starting...');
  return (
    <TamaguiProvider config={config} defaultTheme="electric_blue">
      <GestureHandlerRootView style={{ flex: 1 }}>
        <SafeAreaProvider>
          <Provider store={store}>
            <PersistGate loading={<LoadingScreen message="Initializing app..." />} persistor={persistor}>
              <StatusBar style="auto" />
              <AppContent />
            </PersistGate>
          </Provider>
        </SafeAreaProvider>
      </GestureHandlerRootView>
    </TamaguiProvider>
  );
}
