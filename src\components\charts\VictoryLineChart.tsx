// Victory Native Line Chart Component
import React from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';
import { Victory<PERSON><PERSON>, VictoryLine, VictoryArea, VictoryAxis, VictoryTheme, VictoryScatter } from 'victory-native';

interface DataPoint {
  x: number | string | Date;
  y: number;
}

interface VictoryLineChartProps {
  data: DataPoint[];
  width?: number;
  height?: number;
  title?: string;
  color?: string;
  showPoints?: boolean;
  showArea?: boolean;
  animate?: boolean;
  theme?: any;
}

const VictoryLineChart: React.FC<VictoryLineChartProps> = ({
  data,
  width = Dimensions.get('window').width - 40,
  height = 250,
  title,
  color = '#007AFF',
  showPoints = true,
  showArea = false,
  animate = true,
  theme = VictoryTheme.material,
}) => {
  if (data.length === 0) {
    return (
      <View style={[styles.container, { width, height }]}>
        {title && <Text style={styles.title}>{title}</Text>}
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>No data available</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { width, height }]}>
      {title && <Text style={styles.title}>{title}</Text>}
      
      <VictoryChart
        theme={theme}
        width={width}
        height={height - 60} // Reserve space for title
        padding={{ left: 60, top: 20, right: 40, bottom: 60 }}
        animate={animate ? { duration: 1000, onLoad: { duration: 500 } } : false}
      >
        <VictoryAxis
          dependentAxis
          tickFormat={(t) => `${t}`}
          style={{
            tickLabels: { fontSize: 12, fill: '#8E8E93' },
            grid: { stroke: '#E5E5EA', strokeWidth: 1 },
            axis: { stroke: '#C6C6C8', strokeWidth: 1 },
          }}
        />
        <VictoryAxis
          tickFormat={(t) => `${t}`}
          style={{
            tickLabels: { fontSize: 12, fill: '#8E8E93' },
            grid: { stroke: '#E5E5EA', strokeWidth: 1 },
            axis: { stroke: '#C6C6C8', strokeWidth: 1 },
          }}
        />
        
        {showArea && (
          <VictoryArea
            data={data}
            style={{
              data: { 
                fill: color, 
                fillOpacity: 0.2,
                stroke: color,
                strokeWidth: 2,
              },
            }}
            animate={animate ? { duration: 1000 } : false}
          />
        )}
        
        <VictoryLine
          data={data}
          style={{
            data: { 
              stroke: color, 
              strokeWidth: 3,
              strokeLinecap: 'round',
              strokeLinejoin: 'round',
            },
          }}
          animate={animate ? { duration: 1000 } : false}
        />
        
        {showPoints && (
          <VictoryScatter
            data={data}
            size={4}
            style={{
              data: { 
                fill: color,
                stroke: '#FFFFFF',
                strokeWidth: 2,
              },
            }}
            animate={animate ? { duration: 1000 } : false}
          />
        )}
      </VictoryChart>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginVertical: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 12,
    textAlign: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 14,
    color: '#8E8E93',
    textAlign: 'center',
  },
});

export default VictoryLineChart;
