// Reset Options Screen - Factory Reset and Dashboard Data Reset

import React, { useState } from 'react';
import { View, Text, ScrollView, StyleSheet, Alert, Modal, TextInput } from 'react-native';
import { useAppDispatch } from '@/store';
import { resetAllData, resetDashboardData } from '@/store/slices/userSlice';
import { resetSettings } from '@/store/slices/settingsSlice';
import { clearAllPurchases } from '@/store/slices/purchasesSlice';
import { clearAllUsage } from '@/store/slices/usageSlice';
import Container from '@/components/common/Container';
import QuickActionButton from '@/components/common/QuickActionButton';

const ResetOptionsScreen: React.FC = () => {
  const dispatch = useAppDispatch();
  const [showFactoryResetModal, setShowFactoryResetModal] = useState(false);
  const [showDataResetModal, setShowDataResetModal] = useState(false);
  const [confirmationText, setConfirmationText] = useState('');

  const handleFactoryReset = () => {
    setShowFactoryResetModal(true);
  };

  const handleDataReset = () => {
    setShowDataResetModal(true);
  };

  const confirmFactoryReset = async () => {
    if (confirmationText.toLowerCase() !== 'reset') {
      Alert.alert('Error', 'Please type "RESET" to confirm factory reset');
      return;
    }

    try {
      // Reset all data and settings
      await dispatch(resetAllData()).unwrap();
      await dispatch(resetSettings()).unwrap();
      await dispatch(clearAllPurchases()).unwrap();
      await dispatch(clearAllUsage()).unwrap();

      setShowFactoryResetModal(false);
      setConfirmationText('');
      
      Alert.alert(
        'Factory Reset Complete',
        'All data has been cleared. The app will restart with initial setup.',
        [
          {
            text: 'OK',
            onPress: () => {
              // Navigate to initial setup or restart app
            },
          },
        ]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to perform factory reset. Please try again.');
    }
  };

  const confirmDataReset = async () => {
    if (confirmationText.toLowerCase() !== 'clear') {
      Alert.alert('Error', 'Please type "CLEAR" to confirm data reset');
      return;
    }

    try {
      // Reset only dashboard data, keep settings
      await dispatch(clearAllPurchases()).unwrap();
      await dispatch(clearAllUsage()).unwrap();

      setShowDataResetModal(false);
      setConfirmationText('');
      
      Alert.alert(
        'Dashboard Data Reset Complete',
        'All purchase and usage data has been cleared. Your settings have been preserved.',
        [{ text: 'OK' }]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to reset dashboard data. Please try again.');
    }
  };

  const renderResetModal = (
    visible: boolean,
    onClose: () => void,
    onConfirm: () => void,
    title: string,
    description: string,
    confirmWord: string,
    isDangerous: boolean = false
  ) => (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContainer}>
          <Text style={styles.modalTitle}>{title}</Text>
          <Text style={styles.modalDescription}>{description}</Text>
          
          <View style={styles.warningContainer}>
            <Text style={styles.warningText}>
              ⚠️ This action cannot be undone!
            </Text>
          </View>

          <View style={styles.confirmationContainer}>
            <Text style={styles.confirmationLabel}>
              Type "{confirmWord}" to confirm:
            </Text>
            <TextInput
              style={[styles.confirmationInput, isDangerous && styles.dangerInput]}
              value={confirmationText}
              onChangeText={setConfirmationText}
              placeholder={confirmWord}
              autoCapitalize="none"
            />
          </View>

          <View style={styles.modalButtons}>
            <QuickActionButton
              title="Cancel"
              icon="x"
              onPress={() => {
                onClose();
                setConfirmationText('');
              }}
              variant="secondary"
              style={styles.modalButton}
            />
            <QuickActionButton
              title="Confirm"
              icon="check"
              onPress={onConfirm}
              variant="primary"
              style={[styles.modalButton, isDangerous && styles.dangerButton]}
            />
          </View>
        </View>
      </View>
    </Modal>
  );

  return (
    <Container safeArea>
      <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
        <Text style={styles.title}>Reset Options</Text>
        
        <Text style={styles.subtitle}>
          Choose the type of reset you want to perform. Please read the descriptions carefully.
        </Text>

        {/* Dashboard Data Reset */}
        <View style={styles.resetSection}>
          <View style={styles.resetHeader}>
            <Text style={styles.resetTitle}>Dashboard Data Reset</Text>
            <Text style={styles.resetBadge}>Safe</Text>
          </View>
          
          <Text style={styles.resetDescription}>
            This will clear all your purchase and usage history, but keep your app settings, 
            theme preferences, and configuration intact.
          </Text>
          
          <View style={styles.resetDetails}>
            <Text style={styles.detailItem}>✓ Keeps your settings</Text>
            <Text style={styles.detailItem}>✓ Keeps your theme</Text>
            <Text style={styles.detailItem}>✓ Keeps your preferences</Text>
            <Text style={[styles.detailItem, styles.removedItem]}>✗ Removes purchase history</Text>
            <Text style={[styles.detailItem, styles.removedItem]}>✗ Removes usage history</Text>
          </View>

          <QuickActionButton
            title="Reset Dashboard Data"
            icon="refresh"
            onPress={handleDataReset}
            variant="primary"
            style={styles.resetButton}
          />
        </View>

        {/* Factory Reset */}
        <View style={styles.resetSection}>
          <View style={styles.resetHeader}>
            <Text style={styles.resetTitle}>Factory Reset</Text>
            <Text style={[styles.resetBadge, styles.dangerBadge]}>Danger</Text>
          </View>
          
          <Text style={styles.resetDescription}>
            This will completely reset the app to its initial state, removing ALL data, 
            settings, and preferences. You'll need to set up the app again from scratch.
          </Text>
          
          <View style={styles.resetDetails}>
            <Text style={[styles.detailItem, styles.removedItem]}>✗ Removes all settings</Text>
            <Text style={[styles.detailItem, styles.removedItem]}>✗ Removes theme preferences</Text>
            <Text style={[styles.detailItem, styles.removedItem]}>✗ Removes all data</Text>
            <Text style={[styles.detailItem, styles.removedItem]}>✗ Requires initial setup</Text>
          </View>

          <QuickActionButton
            title="Factory Reset"
            icon="alert-triangle"
            onPress={handleFactoryReset}
            variant="primary"
            style={[styles.resetButton, styles.dangerButton]}
          />
        </View>

        {/* Modals */}
        {renderResetModal(
          showDataResetModal,
          () => setShowDataResetModal(false),
          confirmDataReset,
          'Reset Dashboard Data',
          'This will clear all purchase and usage data while preserving your settings.',
          'CLEAR',
          false
        )}

        {renderResetModal(
          showFactoryResetModal,
          () => setShowFactoryResetModal(false),
          confirmFactoryReset,
          'Factory Reset',
          'This will completely reset the app and remove ALL data and settings.',
          'RESET',
          true
        )}
      </ScrollView>
    </Container>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 12,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#8E8E93',
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 22,
  },
  resetSection: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 20,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  resetHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  resetTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#000000',
  },
  resetBadge: {
    backgroundColor: '#34C759',
    color: '#FFFFFF',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    fontSize: 12,
    fontWeight: '600',
  },
  dangerBadge: {
    backgroundColor: '#FF3B30',
  },
  resetDescription: {
    fontSize: 14,
    color: '#8E8E93',
    lineHeight: 20,
    marginBottom: 16,
  },
  resetDetails: {
    marginBottom: 20,
  },
  detailItem: {
    fontSize: 14,
    color: '#34C759',
    marginBottom: 4,
  },
  removedItem: {
    color: '#FF3B30',
  },
  resetButton: {
    marginTop: 8,
  },
  dangerButton: {
    backgroundColor: '#FF3B30',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 24,
    margin: 20,
    maxWidth: 400,
    width: '90%',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#000000',
    textAlign: 'center',
    marginBottom: 12,
  },
  modalDescription: {
    fontSize: 14,
    color: '#8E8E93',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 16,
  },
  warningContainer: {
    backgroundColor: '#FFF3CD',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  warningText: {
    fontSize: 14,
    color: '#856404',
    textAlign: 'center',
    fontWeight: '600',
  },
  confirmationContainer: {
    marginBottom: 24,
  },
  confirmationLabel: {
    fontSize: 14,
    color: '#000000',
    marginBottom: 8,
    fontWeight: '600',
  },
  confirmationInput: {
    borderWidth: 2,
    borderColor: '#007AFF',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  dangerInput: {
    borderColor: '#FF3B30',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  modalButton: {
    flex: 1,
  },
});

export default ResetOptionsScreen;
