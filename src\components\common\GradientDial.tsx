// Enhanced Gradient Dial Component - Modern circular progress indicator with lightning effects

import React, { useEffect, useRef } from 'react';
import { View, Text, Animated, Dimensions, StyleSheet } from 'react-native';
import Svg, { Circle, Defs, LinearGradient, Stop, Path, G } from 'react-native-svg';
import { GradientDialProps } from '@/types/components';

const { width: screenWidth } = Dimensions.get('window');

const AnimatedCircle = Animated.createAnimatedComponent(Circle);

const GradientDial: React.FC<GradientDialProps> = ({
  value,
  maxValue,
  size = Math.min(screenWidth * 0.6, 250),
  strokeWidth = 20,
  colors = ['#007AFF', '#5AC8FA', '#34C759'],
  backgroundColor = '#F2F2F7',
  showValue = true,
  valueFormatter,
  animated = true,
  animationDuration = 1000,
  threshold,
  thresholdColor = '#FF3B30',
  onValueChange,
  style,
  title,
  subtitle,
  showLightning = true,
}) => {
  const animatedValue = useRef(new Animated.Value(0)).current;
  const pulseAnimation = useRef(new Animated.Value(1)).current;
  const radius = (size - strokeWidth) / 2;
  const circumference = 2 * Math.PI * radius;
  const center = size / 2;

  // Calculate progress percentage
  const progress = Math.min(Math.max(value / maxValue, 0), 1);
  const strokeDashoffset = circumference * (1 - progress);

  // Calculate threshold position if provided
  const thresholdProgress = threshold ? Math.min(Math.max(threshold / maxValue, 0), 1) : 0;
  const thresholdOffset = circumference * (1 - thresholdProgress);

  // Animate the dial
  useEffect(() => {
    if (animated) {
      Animated.timing(animatedValue, {
        toValue: progress,
        duration: animationDuration,
        useNativeDriver: false,
      }).start();
    } else {
      animatedValue.setValue(progress);
    }
  }, [progress, animated, animationDuration, animatedValue]);

  // Pulse animation for low units warning
  useEffect(() => {
    if (isBelowThreshold) {
      const pulse = Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnimation, {
            toValue: 1.1,
            duration: 800,
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnimation, {
            toValue: 1,
            duration: 800,
            useNativeDriver: true,
          }),
        ])
      );
      pulse.start();
      return () => pulse.stop();
    } else {
      pulseAnimation.setValue(1);
    }
  }, [isBelowThreshold, pulseAnimation]);

  // Format the display value
  const displayValue = valueFormatter ? valueFormatter(value) : value.toString();

  // Determine if we're below threshold
  const isBelowThreshold = threshold && value <= threshold;

  // Choose colors based on threshold
  const dialColors = isBelowThreshold ? [thresholdColor, '#FF6B6B'] : colors;

  // Lightning bolt path for modern design
  const lightningPath = `M${center - 8},${center - 15} L${center + 2},${center - 5} L${center - 2},${center - 5} L${center + 8},${center + 15} L${center - 2},${center + 5} L${center + 2},${center + 5} Z`;

  return (
    <View style={[styles.container, { width: size, height: size }, style]}>
      <Animated.View style={{ transform: [{ scale: pulseAnimation }] }}>
        <Svg width={size} height={size}>
          <Defs>
            <LinearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
              {dialColors.map((color, index) => (
                <Stop
                  key={index}
                  offset={`${(index / (dialColors.length - 1)) * 100}%`}
                  stopColor={color}
                />
              ))}
            </LinearGradient>
            <LinearGradient id="backgroundGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <Stop offset="0%" stopColor="#F8F9FA" />
              <Stop offset="100%" stopColor="#E9ECEF" />
            </LinearGradient>
            <LinearGradient id="glowGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <Stop offset="0%" stopColor={colors[0]} stopOpacity="0.3" />
              <Stop offset="100%" stopColor={colors[colors.length - 1]} stopOpacity="0.1" />
            </LinearGradient>
          </Defs>

          {/* Outer glow circle */}
          <Circle
            cx={center}
            cy={center}
            r={radius + 8}
            stroke="url(#glowGradient)"
            strokeWidth={4}
            fill="transparent"
            opacity={0.4}
          />

          {/* Background Circle */}
          <Circle
            cx={center}
            cy={center}
            r={radius}
            stroke="url(#backgroundGradient)"
            strokeWidth={strokeWidth}
            fill="transparent"
          />

          {/* Threshold Indicator */}
          {threshold && (
            <Circle
              cx={center}
              cy={center}
              r={radius}
              stroke={thresholdColor}
              strokeWidth={3}
              fill="transparent"
              strokeDasharray={`4 ${circumference - 4}`}
              strokeDashoffset={thresholdOffset}
              transform={`rotate(-90 ${center} ${center})`}
              opacity={0.8}
            />
          )}

          {/* Progress Circle */}
          <AnimatedCircle
            cx={center}
            cy={center}
            r={radius}
            stroke="url(#gradient)"
            strokeWidth={strokeWidth}
            fill="transparent"
            strokeDasharray={circumference}
            strokeDashoffset={animated ?
              animatedValue.interpolate({
                inputRange: [0, 1],
                outputRange: [circumference, circumference * (1 - progress)],
              }) : strokeDashoffset
            }
            strokeLinecap="round"
            transform={`rotate(-90 ${center} ${center})`}
          />

          {/* Lightning bolt icon */}
          {showLightning && (
            <G opacity={0.8}>
              <Path
                d={lightningPath}
                fill={isBelowThreshold ? thresholdColor : colors[0]}
                stroke="white"
                strokeWidth={1}
              />
            </G>
          )}
        </Svg>
      </Animated.View>

      {/* Center Content */}
      {showValue && (
        <View style={styles.centerContent}>
          {title && (
            <Text style={[styles.title, { fontSize: size * 0.08 }]}>
              {title}
            </Text>
          )}

          <Text style={[
            styles.value,
            {
              fontSize: size * 0.12,
              color: isBelowThreshold ? thresholdColor : '#000000',
            }
          ]}>
            {displayValue}
          </Text>

          <Text style={[styles.percentage, { fontSize: size * 0.06 }]}>
            {`${Math.round(progress * 100)}%`}
          </Text>

          {subtitle && (
            <Text style={[styles.subtitle, { fontSize: size * 0.05 }]}>
              {subtitle}
            </Text>
          )}
        </View>
      )}

      {/* Warning Indicator */}
      {isBelowThreshold && (
        <Animated.View style={[
          styles.warningIndicator,
          {
            top: size * 0.15,
            right: size * 0.15,
            backgroundColor: thresholdColor,
          }
        ]}>
          <Text style={styles.warningText}>!</Text>
        </Animated.View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  centerContent: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontWeight: '600',
    color: '#8E8E93',
    textAlign: 'center',
    marginBottom: 4,
  },
  value: {
    fontWeight: 'bold',
    textAlign: 'center',
  },
  percentage: {
    color: '#8E8E93',
    marginTop: 4,
    textAlign: 'center',
  },
  subtitle: {
    color: '#8E8E93',
    textAlign: 'center',
    marginTop: 2,
  },
  warningIndicator: {
    position: 'absolute',
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  warningText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default GradientDial;
