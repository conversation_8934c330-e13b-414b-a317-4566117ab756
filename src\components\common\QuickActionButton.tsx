// Enhanced Quick Action Button Component

import React from 'react';
import { TouchableOpacity, Text, StyleSheet, ViewStyle, View } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';

interface QuickActionButtonProps {
  title: string;
  icon: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'narrow';
  disabled?: boolean;
  style?: ViewStyle;
  gradient?: boolean;
  colors?: string[];
}

const QuickActionButton: React.FC<QuickActionButtonProps> = ({
  title,
  icon,
  onPress,
  variant = 'primary',
  disabled = false,
  style,
  gradient = false,
  colors = ['#007AFF', '#5AC8FA'],
}) => {
  const getButtonStyle = () => {
    const baseStyle = [styles.button];

    if (variant === 'narrow') {
      baseStyle.push(styles.narrowButton);
    } else if (variant === 'primary') {
      baseStyle.push(styles.primaryButton);
    } else {
      baseStyle.push(styles.secondaryButton);
    }

    if (disabled) {
      baseStyle.push(styles.disabledButton);
    }

    return [...baseStyle, style];
  };

  const getTextStyle = () => {
    const baseStyle = [styles.text];

    if (variant === 'narrow') {
      baseStyle.push(styles.narrowText);
    } else if (variant === 'primary') {
      baseStyle.push(styles.primaryText);
    } else {
      baseStyle.push(styles.secondaryText);
    }

    if (disabled) {
      baseStyle.push(styles.disabledText);
    }

    return baseStyle;
  };

  const renderContent = () => (
    <View style={styles.content}>
      <Text style={styles.iconText}>{getIconSymbol(icon)}</Text>
      <Text style={getTextStyle()}>{title}</Text>
    </View>
  );

  if (gradient && !disabled && variant !== 'secondary') {
    return (
      <TouchableOpacity
        onPress={onPress}
        disabled={disabled}
        activeOpacity={0.7}
        style={style}
      >
        <LinearGradient
          colors={colors}
          style={getButtonStyle()}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          {renderContent()}
        </LinearGradient>
      </TouchableOpacity>
    );
  }

  return (
    <TouchableOpacity
      style={getButtonStyle()}
      onPress={onPress}
      disabled={disabled}
      activeOpacity={0.7}
    >
      {renderContent()}
    </TouchableOpacity>
  );
};

// Helper function to get icon symbols
const getIconSymbol = (iconName: string): string => {
  const iconMap: { [key: string]: string } = {
    'plus-circle': '⊕',
    'activity': '⚡',
    'clock': '🕐',
    'settings': '⚙️',
    'history': '📊',
    'purchase': '💰',
    'usage': '📈',
    'lightning': '⚡',
    'bolt': '⚡',
    'add': '+',
    'record': '📝',
    'view': '👁️',
  };

  return iconMap[iconName] || '•';
};

const styles = StyleSheet.create({
  button: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 48,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  narrowButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    minHeight: 36,
    borderRadius: 6,
    flexDirection: 'row',
    backgroundColor: '#007AFF',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  primaryButton: {
    backgroundColor: '#007AFF',
  },
  secondaryButton: {
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#007AFF',
  },
  disabledButton: {
    backgroundColor: '#F2F2F7',
    borderColor: '#C6C6C8',
    shadowOpacity: 0,
    elevation: 0,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconText: {
    fontSize: 18,
    marginRight: 8,
    color: '#FFFFFF',
  },
  text: {
    fontSize: 16,
    fontWeight: '600',
  },
  narrowText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  primaryText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  secondaryText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#007AFF',
  },
  disabledText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#8E8E93',
  },
});

export default QuickActionButton;
